# SolAI - Real-Time Conversational AI System

A complete real-time conversational AI system built with **LangGraph**, **LiveKit**, and **Flutter Web**. This system enables natural speech-to-speech conversations with an AI assistant using cutting-edge technologies.

## 🚀 Features

- **Real-Time Voice Conversations**: Natural speech-to-speech interaction with AI
- **LangGraph Integration**: Advanced conversation flow and memory management
- **LiveKit WebRTC**: Low-latency, high-quality audio streaming
- **Flutter Web Frontend**: Modern, responsive web interface
- **Multi-Provider Support**: OpenAI, Google Gemini, ElevenLabs, Deepgram
- **Session Memory**: Maintains conversation context and history
- **Cross-Platform**: Runs on web, mobile, and desktop

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter Web   │    │  Token Server   │    │  LiveKit Agent  │
│   (Frontend)    │◄──►│   (Backend)     │    │   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         └──────────────── LiveKit Room ──────────────┘
                              │
                    ┌─────────────────┐
                    │  LangGraph Flow │
                    │   (AI Logic)    │
                    └─────────────────┘
```

## 🛠️ Technology Stack

### Backend
- **Python 3.8+**
- **LiveKit Agents**: Real-time audio processing
- **LangGraph**: Conversation flow management
- **<PERSON><PERSON>hain**: AI integration framework
- **OpenAI/Gemini**: Language models
- **Flask**: Token generation server

### Frontend
- **Flutter Web**: Cross-platform UI framework
- **LiveKit Client**: WebRTC audio streaming
- **Provider**: State management
- **Material Design 3**: Modern UI components

## 📋 Prerequisites

1. **Python 3.8+** with pip
2. **Flutter SDK** (latest stable)
3. **LiveKit Server** (cloud or self-hosted)
4. **API Keys**:
   - OpenAI API key
   - LiveKit API credentials
   - Google API key (optional)

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone <your-repo>
cd solai
```

### 2. Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your API keys
```

### 3. Frontend Setup
```bash
cd frontend
flutter pub get
# Edit .env with LiveKit configuration
```

### 4. Run the System
```bash
# Terminal 1: Token Server
cd backend && python token_server.py

# Terminal 2: LiveKit Agent
cd backend && python main.py

# Terminal 3: Flutter Web App
cd frontend && flutter run -d web-server --web-port 3000
```

### 5. Test the Application
1. Open `http://localhost:3000`
2. Click "Connect"
3. Allow microphone permissions
4. Start speaking!

## 📖 Detailed Setup

For detailed setup instructions, see [setup.md](setup.md).

## 🔧 Configuration

### Backend Environment Variables
```env
# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# AI Configuration
OPENAI_API_KEY=your-openai-key
GOOGLE_API_KEY=your-google-key  # Optional

# Agent Settings
AGENT_NAME=SolAI Assistant
LLM_MODEL=gpt-4o-mini
```

### Frontend Environment Variables
```env
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret
```

## 🎯 Key Components

### Backend Components
- **`agent.py`**: Main LiveKit agent with LangGraph integration
- **`langgraph_flow.py`**: Conversation flow and memory management
- **`token_server.py`**: JWT token generation for LiveKit
- **`config.py`**: Configuration management

### Frontend Components
- **`main.dart`**: App entry point and configuration
- **`livekit_service.dart`**: LiveKit client integration
- **`voice_chat_screen.dart`**: Main voice chat interface

## 🔍 Features in Detail

### Real-Time Audio Processing
- Echo cancellation and noise suppression
- Voice Activity Detection (VAD)
- Turn detection and interruption handling
- Adaptive bitrate streaming

### AI Conversation Management
- LangGraph-powered conversation flows
- Session-based memory and context
- Multi-turn conversation support
- Customizable AI personality and behavior

### User Interface
- Modern Material Design 3 interface
- Real-time connection status indicators
- Animated voice activity visualization
- Responsive design for all screen sizes

## 🚀 Production Deployment

### Backend Deployment
1. Set up production LiveKit server
2. Deploy to cloud platform (AWS, GCP, Azure)
3. Configure environment variables
4. Set up monitoring and logging

### Frontend Deployment
1. Build: `flutter build web`
2. Deploy to web hosting service
3. Configure HTTPS (required for microphone access)
4. Update API endpoints

## 🛠️ Development

### Adding New AI Providers
1. Install provider SDK in `requirements.txt`
2. Add configuration in `config.py`
3. Integrate in `langgraph_flow.py`

### Customizing Conversation Flow
Edit `langgraph_flow.py` to modify:
- AI personality and behavior
- Conversation logic and routing
- Memory management
- Tool integration

### Extending the UI
Modify Flutter components in `frontend/lib/`:
- Add new screens and widgets
- Customize themes and styling
- Implement additional features

## 📚 Documentation

- [Backend README](backend/README.md)
- [Frontend README](frontend/README.md)
- [Setup Guide](setup.md)
- [Project Overview](PROJECT_OVERVIEW.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [LiveKit](https://livekit.io/) for real-time communication infrastructure
- [LangGraph](https://github.com/langchain-ai/langgraph) for conversation flow management
- [Flutter](https://flutter.dev/) for cross-platform UI framework
- [OpenAI](https://openai.com/) for language models and audio processing
