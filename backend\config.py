"""Configuration management for the SolAI backend."""

from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # LiveKit Configuration
    livekit_url: str = Field(..., env="LIVEKIT_URL")
    livekit_api_key: str = Field(..., env="LIVEKIT_API_KEY")
    livekit_api_secret: str = Field(..., env="LIVEKIT_API_SECRET")

    # AI Provider Configuration
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")

    # Optional AI Providers
    elevenlabs_api_key: Optional[str] = Field(None, env="ELEVENLABS_API_KEY")
    deepgram_api_key: Optional[str] = Field(None, env="DEEPGRAM_API_KEY")

    # Agent Configuration
    agent_name: str = Field("SolAI Assistant", env="AGENT_NAME")
    agent_room_prefix: str = Field("solai-room-", env="AGENT_ROOM_PREFIX")
    log_level: str = Field("INFO", env="LOG_LEVEL")

    # Provider Selection
    llm_provider: str = Field("gemini", env="LLM_PROVIDER")
    stt_provider: str = Field("gemini", env="STT_PROVIDER")
    tts_provider: str = Field("elevenlabs", env="TTS_PROVIDER")

    # Model Configuration
    gemini_model: str = Field("gemini-2.5-flash-preview-05-20", env="GEMINI_MODEL")
    elevenlabs_voice_id: str = Field("21m00Tcm4TlvDq8ikWAM", env="ELEVENLABS_VOICE_ID")

    # Legacy fields for backward compatibility
    llm_model: str = Field("gpt-4o-mini", env="LLM_MODEL")
    tts_voice: str = Field("alloy", env="TTS_VOICE")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()
