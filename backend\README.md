# SolAI Backend - LiveKit Agent with LangGraph

This is the backend component of the SolAI real-time conversational AI system. It uses LiveKit Agents for real-time audio streaming and LangGraph for conversation flow management.

## Features

- **LiveKit Agent**: Real-time audio streaming with WebRTC
- **LangGraph Integration**: Advanced conversation flow and memory management
- **Multiple AI Providers**: Support for OpenAI, Google Gemini, ElevenLabs, Deepgram
- **Voice Assistant**: Complete speech-to-speech pipeline
- **Session Management**: Per-user conversation memory and context

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and LiveKit configuration
   ```

3. **Required API Keys**:
   - LiveKit server URL, API key, and secret
   - OpenAI API key (for LLM and TTS/STT)
   - Google API key (optional, for Gemini)
   - ElevenLabs API key (optional, for advanced TTS)
   - Deepgram API key (optional, for advanced STT)

## Running the Agent

```bash
python main.py
```

The agent will start and wait for connections from the Flutter frontend.

## Configuration

Edit `config.py` or set environment variables:

- `LIVEKIT_URL`: Your LiveKit server WebSocket URL
- `LIVEKIT_API_KEY`: LiveKit API key
- `LIVEKIT_API_SECRET`: LiveKit API secret
- `OPENAI_API_KEY`: OpenAI API key
- `GOOGLE_API_KEY`: Google API key (optional)
- `AGENT_NAME`: Name of your AI assistant
- `LLM_MODEL`: LLM model to use (default: gpt-4o-mini)

## Architecture

- **agent.py**: Main LiveKit agent implementation
- **langgraph_flow.py**: LangGraph conversation flow
- **config.py**: Configuration management
- **main.py**: Entry point

## Development

The agent integrates:
1. LiveKit for real-time audio streaming
2. LangGraph for conversation logic and memory
3. OpenAI/Gemini for language understanding
4. TTS/STT for voice processing
5. VAD for turn detection

## Production Deployment

For production deployment:
1. Set up a LiveKit server (cloud or self-hosted)
2. Configure proper environment variables
3. Use Docker for containerization
4. Set up monitoring and logging
5. Implement proper error handling and recovery
