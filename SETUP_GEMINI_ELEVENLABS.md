# SolAI Setup Guide - Gemini + ElevenLabs Configuration

This guide is specifically for setting up SolAI with:
- **Gemini** for LLM (Language Model)
- **Gemini** for STT (Speech-to-Text) - *Note: Currently falls back to OpenAI STT*
- **ElevenLabs** for TTS (Text-to-Speech)

## 🔑 Required API Keys

You'll need the following API keys:

1. **LiveKit Credentials**:
   - LiveKit URL (WebSocket URL)
   - LiveKit API Key
   - LiveKit API Secret

2. **Google API Key**:
   - For Gemini LLM
   - Get from: [Google AI Studio](https://makersuite.google.com/app/apikey)

3. **ElevenLabs API Key**:
   - For high-quality TTS
   - Get from: [ElevenLabs](https://elevenlabs.io/app/settings/api-keys)

4. **OpenAI API Key** (fallback for STT):
   - Currently needed for Speech-to-Text
   - Get from: [OpenAI Platform](https://platform.openai.com/api-keys)

## 📝 Environment Configuration

### Backend Configuration (`backend/.env`)

Create or update your `backend/.env` file:

```env
# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# AI Provider Configuration
GOOGLE_API_KEY=your-google-api-key
ELEVENLABS_API_KEY=your-elevenlabs-api-key
OPENAI_API_KEY=your-openai-api-key

# Provider Selection
LLM_PROVIDER=gemini
STT_PROVIDER=gemini
TTS_PROVIDER=elevenlabs

# Model Configuration
GEMINI_MODEL=gemini-1.5-flash
ELEVENLABS_VOICE_ID=21m00Tcm4TlvDq8ikWAM

# Agent Configuration
AGENT_NAME=SolAI Assistant
AGENT_ROOM_PREFIX=solai-room-
LOG_LEVEL=INFO
```

### Frontend Configuration (`frontend/.env`)

```env
# LiveKit Configuration for Flutter Web
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# App Configuration
APP_NAME=SolAI Assistant
```

## 🎙️ ElevenLabs Voice Configuration

### Finding Your Voice ID

1. Go to [ElevenLabs Voice Lab](https://elevenlabs.io/app/voice-lab)
2. Choose or create a voice
3. Copy the Voice ID from the voice settings
4. Update `ELEVENLABS_VOICE_ID` in your `.env` file

### Popular Voice IDs

- `21m00Tcm4TlvDq8ikWAM` - Rachel (default)
- `AZnzlk1XvdvUeBnXmlld` - Domi
- `EXAVITQu4vr4xnSDxMaL` - Bella
- `ErXwobaYiN019PkySvjV` - Antoni
- `MF3mGyEYCl7XYWbV9V6O` - Elli
- `TxGEqnHWrfWFTfGW9XjX` - Josh

## 🧠 Gemini Model Options

You can use different Gemini models by updating `GEMINI_MODEL`:

- `gemini-1.5-flash` (default) - Fast and efficient
- `gemini-1.5-pro` - More capable but slower
- `gemini-1.0-pro` - Legacy model

## 🚀 Installation & Setup

### 1. Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Test configuration
python test_config.py
```

### 2. Frontend Setup

```bash
cd frontend

# Install Flutter dependencies
flutter pub get
```

### 3. Verify Configuration

Run the configuration test:

```bash
cd backend
python test_config.py
```

This will check:
- ✅ All API keys are present
- ✅ Provider configuration is correct
- ✅ Required packages can be imported

## 🏃‍♂️ Running the System

### Terminal 1: Token Server
```bash
cd backend
python token_server.py
```
*Starts on http://localhost:8080*

### Terminal 2: LiveKit Agent
```bash
cd backend
python main.py
```
*Connects to LiveKit and waits for users*

### Terminal 3: Flutter Web App
```bash
cd frontend
flutter run -d web-server --web-port 3000
```
*Starts on http://localhost:3000*

## 🔧 Troubleshooting

### Common Issues

1. **"Google API Key not working"**:
   - Ensure you've enabled the Generative AI API
   - Check quota limits in Google Cloud Console

2. **"ElevenLabs voice not found"**:
   - Verify the Voice ID is correct
   - Check your ElevenLabs subscription status

3. **"STT falling back to OpenAI"**:
   - This is expected - Gemini STT isn't directly supported in LiveKit yet
   - Ensure OpenAI API key is set for STT fallback

4. **"Agent not responding"**:
   - Check that Gemini API key has sufficient quota
   - Verify the model name is correct

### Debug Mode

Enable detailed logging:

```bash
# In backend/.env
LOG_LEVEL=DEBUG
```

### Testing Individual Components

Test Gemini connection:
```python
from langchain_google_genai import ChatGoogleGenerativeAI
llm = ChatGoogleGenerativeAI(model="gemini-1.5-flash", google_api_key="your-key")
response = llm.invoke("Hello, how are you?")
print(response.content)
```

Test ElevenLabs:
```python
import elevenlabs
elevenlabs.set_api_key("your-key")
audio = elevenlabs.generate(text="Hello world", voice="Rachel")
```

## 🎯 Expected Behavior

With this configuration:

1. **User speaks** → OpenAI STT transcribes
2. **Text processed** → Gemini generates response
3. **Response synthesized** → ElevenLabs creates audio
4. **Audio streamed** → User hears natural voice

## 📈 Performance Tips

1. **Gemini Model Selection**:
   - Use `gemini-1.5-flash` for faster responses
   - Use `gemini-1.5-pro` for better quality

2. **ElevenLabs Optimization**:
   - Choose voices with lower latency
   - Consider voice cloning for custom voices

3. **Network Optimization**:
   - Use a LiveKit server close to your location
   - Ensure stable internet connection

## 🔄 Upgrading

To update to newer models or voices:

1. Update model names in `.env`
2. Restart the backend services
3. Test with the configuration script

## 📞 Support

If you encounter issues:

1. Run `python test_config.py` to verify setup
2. Check the logs for detailed error messages
3. Ensure all API keys have sufficient quota
4. Verify network connectivity to all services
