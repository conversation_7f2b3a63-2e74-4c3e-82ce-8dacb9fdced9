#!/usr/bin/env python3
"""Test token generation and validation."""

import jwt
import time
from datetime import datetime, timedelta
from config import settings

def test_token_generation():
    """Test JWT token generation."""
    print("🔧 Testing JWT Token Generation...")
    print("=" * 50)
    
    room_name = "test-room"
    participant_name = "test-user"
    
    try:
        # Generate token
        now = datetime.utcnow()
        exp = now + timedelta(hours=6)
        
        payload = {
            'iss': settings.livekit_api_key,
            'sub': participant_name,
            'iat': int(now.timestamp()),
            'exp': int(exp.timestamp()),
            'room': room_name,
            'grants': {
                'room': room_name,
                'roomJoin': True,
                'canPublish': True,
                'canSubscribe': True,
            }
        }
        
        print(f"📋 Token Payload:")
        print(f"  Issuer: {payload['iss']}")
        print(f"  Subject: {payload['sub']}")
        print(f"  Room: {payload['room']}")
        print(f"  Issued At: {datetime.fromtimestamp(payload['iat'])}")
        print(f"  Expires At: {datetime.fromtimestamp(payload['exp'])}")
        
        token = jwt.encode(payload, settings.livekit_api_secret, algorithm='HS256')
        print(f"\n✅ Token Generated: {token[:50]}...")
        
        # Validate token
        decoded = jwt.decode(token, settings.livekit_api_secret, algorithms=['HS256'])
        print(f"\n✅ Token Validation Successful!")
        print(f"  Decoded Room: {decoded['room']}")
        print(f"  Decoded Subject: {decoded['sub']}")
        
        return token
        
    except Exception as e:
        print(f"❌ Token generation failed: {e}")
        return None

def test_livekit_connection():
    """Test basic LiveKit connection."""
    print("\n🔗 Testing LiveKit Connection...")
    print("=" * 50)
    
    print(f"LiveKit URL: {settings.livekit_url}")
    print(f"API Key: {settings.livekit_api_key}")
    print(f"API Secret: {settings.livekit_api_secret[:8]}...")
    
    # Check URL format
    if not settings.livekit_url.startswith('wss://'):
        print("⚠️  Warning: LiveKit URL should start with 'wss://'")
    
    if not settings.livekit_api_key or not settings.livekit_api_secret:
        print("❌ Missing LiveKit credentials")
        return False
    
    print("✅ LiveKit configuration looks valid")
    return True

if __name__ == "__main__":
    print("🎯 LiveKit Token and Connection Test")
    print("=" * 50)
    
    # Test configuration
    config_ok = test_livekit_connection()
    
    # Test token generation
    token = test_token_generation()
    
    print("\n" + "=" * 50)
    if config_ok and token:
        print("🎉 All tests passed!")
        print(f"✅ Token: {token}")
    else:
        print("❌ Some tests failed.")
