"""Simple token generation server for LiveKit."""

import jwt
import time
from datetime import datetime, timedelta
from flask import Flask, request, jsonify
from flask_cors import CORS
from config import settings

app = Flask(__name__)
CORS(app)  # Enable CORS for web frontend


def generate_access_token(room_name: str, participant_name: str) -> str:
    """Generate a LiveKit access token."""
    now = datetime.utcnow()
    exp = now + timedelta(hours=6)  # Token expires in 6 hours
    
    payload = {
        'iss': settings.livekit_api_key,
        'sub': participant_name,
        'iat': int(now.timestamp()),
        'exp': int(exp.timestamp()),
        'room': room_name,
        'grants': {
            'room': room_name,
            'roomJoin': True,
            'canPublish': True,
            'canSubscribe': True,
        }
    }
    
    token = jwt.encode(payload, settings.livekit_api_secret, algorithm='HS256')
    return token


@app.route('/token', methods=['POST'])
def create_token():
    """Create a LiveKit access token."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        room_name = data.get('room')
        participant_name = data.get('participant', 'user')
        
        if not room_name:
            return jsonify({'error': 'Room name is required'}), 400
        
        token = generate_access_token(room_name, participant_name)
        
        return jsonify({
            'token': token,
            'room': room_name,
            'participant': participant_name,
            'url': settings.livekit_url
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()})


if __name__ == '__main__':
    print(f"Starting token server...")
    print(f"LiveKit URL: {settings.livekit_url}")
    app.run(host='0.0.0.0', port=8080, debug=True)
