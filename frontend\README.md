# SolAI Frontend - Flutter Web

This is the Flutter Web frontend for the SolAI real-time conversational AI system. It provides a clean, modern interface for voice conversations with the AI assistant.

## Features

- **Real-time Voice Chat**: Direct audio streaming to LiveKit
- **Modern UI**: Clean, responsive design with animations
- **Connection Management**: Easy connect/disconnect controls
- **Audio Controls**: Mute/unmute functionality
- **Status Indicators**: Visual feedback for connection and audio states
- **Cross-platform**: Runs on web, mobile, and desktop

## Setup

1. **Install Flutter**: Make sure you have Flutter installed and configured for web development.

2. **Install Dependencies**:
   ```bash
   flutter pub get
   ```

3. **Environment Configuration**:
   ```bash
   # Edit frontend/.env with your LiveKit configuration
   LIVEKIT_URL=wss://your-livekit-server.com
   LIVEKIT_API_KEY=your-api-key
   LIVEKIT_API_SECRET=your-api-secret
   ```

## Running the App

```bash
flutter run -d web-server --web-port 3000
```

The app will be available at `http://localhost:3000`
