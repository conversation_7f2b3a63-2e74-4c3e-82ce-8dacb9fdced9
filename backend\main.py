"""Main entry point for the SolAI LiveKit Agent."""

import asyncio
import logging
from agent import main

if __name__ == "__main__":
    # Setup basic logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("Starting SolAI LiveKit Agent...")
    
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Agent stopped by user")
    except Exception as e:
        logger.error(f"Agent failed with error: {e}")
        raise
