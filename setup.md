# SolAI Setup Guide

This guide will help you set up the complete SolAI system with LangGraph, LiveKit, and Flutter Web.

## Prerequisites

1. **Python 3.8+** installed
2. **Flutter SDK** installed and configured for web
3. **LiveKit Server** (cloud or self-hosted)
4. **API Keys** for AI services

## Step 1: LiveKit Server Setup

### Option A: LiveKit Cloud (Recommended for testing)
1. Go to [LiveKit Cloud](https://cloud.livekit.io/)
2. Create an account and project
3. Get your WebSocket URL, API Key, and API Secret

### Option B: Self-hosted LiveKit Server
```bash
# Using Docker
docker run --rm -p 7880:7880 -p 7881:7881 -p 7882:7882/udp \
  livekit/livekit-server --dev
```

## Step 2: Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Required environment variables**:
   ```env
   # LiveKit Configuration
   LIVEKIT_URL=wss://your-livekit-server.com
   LIVEKIT_API_KEY=your-api-key
   LIVEKIT_API_SECRET=your-api-secret

   # AI Provider Configuration
   OPENAI_API_KEY=your-openai-api-key
   GOOGLE_API_KEY=your-google-api-key  # Optional

   # Agent Configuration
   AGENT_NAME=SolAI Assistant
   ```

## Step 3: Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install Flutter dependencies**:
   ```bash
   flutter pub get
   ```

3. **Configure environment**:
   ```bash
   # Edit frontend/.env
   LIVEKIT_URL=wss://your-livekit-server.com
   LIVEKIT_API_KEY=your-api-key
   LIVEKIT_API_SECRET=your-api-secret
   ```

## Step 4: Running the System

### Terminal 1: Start Token Server
```bash
cd backend
python token_server.py
```
This starts the token generation server on `http://localhost:8080`

### Terminal 2: Start LiveKit Agent
```bash
cd backend
python main.py
```
This starts the LiveKit agent that handles voice conversations

### Terminal 3: Start Flutter Web App
```bash
cd frontend
flutter run -d web-server --web-port 3000
```
This starts the Flutter web app on `http://localhost:3000`

## Step 5: Testing the System

1. Open your browser to `http://localhost:3000`
2. Click "Connect" to join a LiveKit room
3. Allow microphone permissions when prompted
4. Start speaking to test the voice conversation

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter Web   │    │  Token Server   │    │  LiveKit Agent  │
│   (Frontend)    │◄──►│   (Backend)     │    │   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         └──────────────── LiveKit Room ──────────────┘
                              │
                    ┌─────────────────┐
                    │  LangGraph Flow │
                    │   (AI Logic)    │
                    └─────────────────┘
```

## Troubleshooting

### Common Issues

1. **"Token generation failed"**:
   - Ensure token server is running on port 8080
   - Check LiveKit API credentials in backend/.env

2. **"Microphone permission denied"**:
   - Use HTTPS in production
   - Check browser permissions

3. **"Connection failed"**:
   - Verify LiveKit server is running
   - Check WebSocket URL format (wss://)

4. **"Agent not responding"**:
   - Check OpenAI API key
   - Verify agent is running and connected

### Debug Mode

Enable debug logging:
```bash
# Backend
export LOG_LEVEL=DEBUG

# Flutter
flutter run -d web-server --web-port 3000 --debug
```

## Production Deployment

### Backend
1. Use proper environment variables
2. Deploy to cloud service (AWS, GCP, Azure)
3. Set up monitoring and logging
4. Use production LiveKit server

### Frontend
1. Build for production: `flutter build web`
2. Deploy to web hosting service
3. Configure HTTPS (required for microphone)
4. Update API endpoints for production

## Next Steps

1. **Customize AI Behavior**: Edit `langgraph_flow.py` to modify conversation logic
2. **Add More Providers**: Integrate ElevenLabs, Deepgram, etc.
3. **Enhance UI**: Customize the Flutter interface
4. **Add Features**: Implement conversation history, user profiles, etc.
5. **Scale**: Set up load balancing and multiple agent instances

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the individual README files in backend/ and frontend/
3. Consult LiveKit and LangGraph documentation
