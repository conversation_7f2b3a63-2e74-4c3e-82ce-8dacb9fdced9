#!/usr/bin/env python3
"""Test script to verify configuration and API keys."""

import os
import sys
from dotenv import load_dotenv

def test_configuration():
    """Test that all required configuration is present."""
    print("🔧 Testing SolAI Configuration...")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Test LiveKit configuration
    print("\n📡 LiveKit Configuration:")
    livekit_url = os.getenv('LIVEKIT_URL')
    livekit_api_key = os.getenv('LIVEKIT_API_KEY')
    livekit_api_secret = os.getenv('LIVEKIT_API_SECRET')
    
    if livekit_url:
        print(f"  ✅ LIVEKIT_URL: {livekit_url}")
    else:
        print("  ❌ LIVEKIT_URL: Not set")
        
    if livekit_api_key:
        print(f"  ✅ LIVEKIT_API_KEY: {livekit_api_key[:8]}...")
    else:
        print("  ❌ LIVEKIT_API_KEY: Not set")
        
    if livekit_api_secret:
        print(f"  ✅ LIVEKIT_API_SECRET: {livekit_api_secret[:8]}...")
    else:
        print("  ❌ LIVEKIT_API_SECRET: Not set")
    
    # Test AI Provider configuration
    print("\n🤖 AI Provider Configuration:")
    
    # Gemini (for LLM and STT)
    google_api_key = os.getenv('GOOGLE_API_KEY')
    if google_api_key:
        print(f"  ✅ GOOGLE_API_KEY: {google_api_key[:8]}... (Gemini LLM & STT)")
    else:
        print("  ❌ GOOGLE_API_KEY: Not set (required for Gemini)")
    
    # ElevenLabs (for TTS)
    elevenlabs_api_key = os.getenv('ELEVENLABS_API_KEY')
    if elevenlabs_api_key:
        print(f"  ✅ ELEVENLABS_API_KEY: {elevenlabs_api_key[:8]}... (TTS)")
    else:
        print("  ❌ ELEVENLABS_API_KEY: Not set (required for TTS)")
    
    # OpenAI (fallback)
    openai_api_key = os.getenv('OPENAI_API_KEY')
    if openai_api_key:
        print(f"  ✅ OPENAI_API_KEY: {openai_api_key[:8]}... (fallback)")
    else:
        print("  ⚠️  OPENAI_API_KEY: Not set (fallback option)")
    
    # Test Provider Selection
    print("\n⚙️  Provider Selection:")
    llm_provider = os.getenv('LLM_PROVIDER', 'gemini')
    stt_provider = os.getenv('STT_PROVIDER', 'gemini')
    tts_provider = os.getenv('TTS_PROVIDER', 'elevenlabs')
    
    print(f"  🧠 LLM Provider: {llm_provider}")
    print(f"  🎤 STT Provider: {stt_provider}")
    print(f"  🔊 TTS Provider: {tts_provider}")
    
    # Test Agent Configuration
    print("\n🤖 Agent Configuration:")
    agent_name = os.getenv('AGENT_NAME', 'SolAI Assistant')
    gemini_model = os.getenv('GEMINI_MODEL', 'gemini-1.5-flash')
    elevenlabs_voice_id = os.getenv('ELEVENLABS_VOICE_ID', '21m00Tcm4TlvDq8ikWAM')
    
    print(f"  👤 Agent Name: {agent_name}")
    print(f"  🧠 Gemini Model: {gemini_model}")
    print(f"  🔊 ElevenLabs Voice ID: {elevenlabs_voice_id}")
    
    # Summary
    print("\n📋 Configuration Summary:")
    required_keys = [livekit_url, livekit_api_key, livekit_api_secret, google_api_key, elevenlabs_api_key]
    missing_keys = [key for key in required_keys if not key]
    
    if not missing_keys:
        print("  ✅ All required configuration is present!")
        print("  🚀 Ready to start SolAI system")
        return True
    else:
        print(f"  ❌ Missing {len(missing_keys)} required configuration items")
        print("  📝 Please check your .env file")
        return False

def test_imports():
    """Test that required packages can be imported."""
    print("\n📦 Testing Package Imports...")
    print("=" * 50)
    
    packages = [
        ('livekit.agents', 'LiveKit Agents'),
        ('langgraph', 'LangGraph'),
        ('langchain_google_genai', 'LangChain Google GenAI'),
        ('flask', 'Flask'),
        ('jwt', 'PyJWT'),
    ]
    
    all_imports_ok = True
    
    for package, name in packages:
        try:
            __import__(package)
            print(f"  ✅ {name}: OK")
        except ImportError as e:
            print(f"  ❌ {name}: Failed - {e}")
            all_imports_ok = False
    
    return all_imports_ok

if __name__ == "__main__":
    print("🎯 SolAI Configuration Test")
    print("=" * 50)
    
    config_ok = test_configuration()
    imports_ok = test_imports()
    
    print("\n" + "=" * 50)
    if config_ok and imports_ok:
        print("🎉 All tests passed! SolAI is ready to run.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        sys.exit(1)
