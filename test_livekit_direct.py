#!/usr/bin/env python3
"""Direct test of LiveKit connection using Python client."""

import asyncio
import logging
from livekit import api, rtc
import requests
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_livekit_connection():
    """Test direct connection to LiveKit server."""
    
    # LiveKit credentials
    url = "wss://test-6hztxoof.livekit.cloud"
    api_key = "APIddL3tcFuuN4K"
    api_secret = "ae1QD4pWsfsYpB8cDednYdFvPsBqzifwc6nYkR5uOfoG"
    
    print("🔗 Testing LiveKit Connection...")
    print(f"URL: {url}")
    print(f"API Key: {api_key}")
    
    try:
        # First, get a token from our token server
        print("\n📝 Getting token from token server...")
        response = requests.post('http://localhost:8080/token', 
                               json={'room': 'test-room-python', 'participant': 'test-python'})
        
        if response.status_code != 200:
            print(f"❌ Token generation failed: {response.status_code}")
            return False
            
        token_data = response.json()
        token = token_data['token']
        print(f"✅ Token received: {token[:50]}...")
        
        # Create room and connect
        print("\n🏠 Creating room...")
        room = rtc.Room()
        
        # Set up event handlers
        @room.on("connected")
        def on_connected():
            print("✅ Connected to LiveKit room!")
            
        @room.on("disconnected") 
        def on_disconnected():
            print("📤 Disconnected from LiveKit room")
            
        @room.on("connection_quality_changed")
        def on_quality_changed(quality, participant):
            print(f"📊 Connection quality: {quality}")
            
        # Connect to room
        print("🔌 Connecting to room...")
        await room.connect(url, token)
        
        print("✅ Successfully connected to LiveKit!")
        print(f"Room SID: {room.sid}")
        print(f"Participants: {len(room.remote_participants)}")
        
        # Wait a bit to see if everything is stable
        await asyncio.sleep(5)
        
        # Disconnect
        await room.disconnect()
        print("✅ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        logger.exception("Full error details:")
        return False

if __name__ == "__main__":
    print("🎯 LiveKit Direct Connection Test")
    print("=" * 50)
    
    success = asyncio.run(test_livekit_connection())
    
    if success:
        print("\n🎉 LiveKit connection test passed!")
    else:
        print("\n❌ LiveKit connection test failed!")
