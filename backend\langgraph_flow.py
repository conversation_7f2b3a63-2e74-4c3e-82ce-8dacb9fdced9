"""LangGraph conversation flow implementation for SolAI."""

import logging
from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_google_genai import ChatGoogleGenerativeAI
from pydantic import BaseModel
from config import settings

logger = logging.getLogger(__name__)


class ConversationState(BaseModel):
    """State for the conversation graph."""
    messages: List[BaseMessage] = []
    user_id: str = ""
    session_id: str = ""
    context: Dict[str, Any] = {}


class ConversationFlow:
    """LangGraph-based conversation flow manager."""

    def __init__(self):
        self.llm = self._initialize_llm()
        self.graph = self._build_graph()

    def _initialize_llm(self):
        """Initialize the language model based on configuration."""
        if settings.llm_provider == "gemini" and settings.google_api_key:
            logger.info(f"Using Google Gemini ({settings.gemini_model}) as LLM")
            return ChatGoogleGenerativeAI(
                model=settings.gemini_model,
                google_api_key=settings.google_api_key,
                temperature=0.7
            )
        elif settings.llm_provider == "openai" and settings.openai_api_key:
            logger.info(f"Using OpenAI ({settings.llm_model}) as LLM")
            return ChatOpenAI(
                model=settings.llm_model,
                api_key=settings.openai_api_key,
                temperature=0.7
            )
        elif settings.google_api_key:
            # Fallback to Gemini if available
            logger.info(f"Falling back to Google Gemini ({settings.gemini_model}) as LLM")
            return ChatGoogleGenerativeAI(
                model=settings.gemini_model,
                google_api_key=settings.google_api_key,
                temperature=0.7
            )
        elif settings.openai_api_key:
            # Fallback to OpenAI if available
            logger.info(f"Falling back to OpenAI ({settings.llm_model}) as LLM")
            return ChatOpenAI(
                model=settings.llm_model,
                api_key=settings.openai_api_key,
                temperature=0.7
            )
        else:
            raise ValueError("No LLM API key configured. Please set GOOGLE_API_KEY or OPENAI_API_KEY")

    def _build_graph(self) -> StateGraph:
        """Build the LangGraph conversation flow."""

        def process_user_input(state: ConversationState) -> ConversationState:
            """Process user input and generate AI response."""
            try:
                # Get the latest user message
                if not state.messages:
                    return state

                # Add system message if this is the first interaction
                messages = state.messages.copy()
                if len(messages) == 1:
                    system_message = HumanMessage(content=f"""
You are {settings.agent_name}, a helpful and friendly AI assistant.
You are having a real-time voice conversation with a user.
Keep your responses conversational, concise, and engaging.
Respond naturally as if you're speaking, not writing.
""")
                    messages.insert(0, system_message)

                # Generate AI response
                response = self.llm.invoke(messages)

                # Add AI response to messages
                state.messages.append(AIMessage(content=response.content))

                logger.info(f"Generated response: {response.content[:100]}...")
                return state

            except Exception as e:
                logger.error(f"Error processing user input: {e}")
                # Add error response
                error_response = AIMessage(content="I'm sorry, I encountered an error. Could you please try again?")
                state.messages.append(error_response)
                return state

        def should_continue(state: ConversationState) -> str:
            """Determine if conversation should continue."""
            return END

        # Build the graph
        workflow = StateGraph(ConversationState)

        # Add nodes
        workflow.add_node("process_input", process_user_input)

        # Add edges
        workflow.set_entry_point("process_input")
        workflow.add_edge("process_input", END)

        return workflow.compile()

    async def process_message(self, user_message: str, user_id: str, session_id: str) -> str:
        """Process a user message and return AI response."""
        try:
            # Create or update conversation state
            state = ConversationState(
                messages=[HumanMessage(content=user_message)],
                user_id=user_id,
                session_id=session_id
            )

            # Process through the graph
            result = await self.graph.ainvoke(state)

            # Extract the AI response
            if result.messages and len(result.messages) > 1:
                ai_response = result.messages[-1].content
                return ai_response
            else:
                return "I'm sorry, I didn't understand that. Could you please try again?"

        except Exception as e:
            logger.error(f"Error in process_message: {e}")
            return "I'm experiencing some technical difficulties. Please try again."

    async def get_conversation_history(self, session_id: str) -> List[BaseMessage]:
        """Get conversation history for a session."""
        # In a production system, this would retrieve from a database
        # For now, return empty list as we're not persisting state
        return []
