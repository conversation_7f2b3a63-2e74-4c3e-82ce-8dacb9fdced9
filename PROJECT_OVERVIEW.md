# Real-Time Conversational AI Companion

## Overview
This project is a real-time conversational AI system enabling seamless, natural speech-to-speech (S2S) interaction between a user and an AI assistant. The new architecture leverages LiveKit Agents (WebRTC-based),LangGraph for conversational logic and memory, and plugin-based support for Gemini (Google Generative AI), ElevenLabs, Deepgram, and more. The frontend is a cross-platform Flutter app.

## Features
- **Real-Time Speech-to-Speech AI Dialog:** Users speak to the app and receive expressive, natural audio responses from the AI.
- **Conversational Memory:** Maintains context and history for coherent, multi-turn conversations using LangGraph.
- **LiveKit WebRTC Backend:** Reliable, low-latency, bidirectional audio streaming with built-in VAD, turn detection, and session management.
- **Plugin-Based AI Pipeline:** Easily swap LLM, TTS, STT, and VAD providers (e.g., Gemini, ElevenLabs, Deepgram, Silero).
- **Cross-Platform Frontend:** Flutter app for Android, iOS, desktop, and web.
- **Advanced Audio Features:** Noise/echo cancellation, adaptive bitrate, and interruption handling.

## Architecture

### 1. **Frontend (Flutter App)**
- Records user audio (microphone input).
- Connects to LiveKit room via WebRTC.
- Streams audio to the backend agent and receives/playbacks AI audio responses in real time.
- Displays conversation status and controls.

### 2. **Backend (LiveKit Agent)**
- Joins the LiveKit room as an AI participant.
- Receives audio from the user via WebRTC.
- Uses VAD and turn detection to segment speech.
- Transcribes audio (e.g., Deepgram, Whisper).
- Runs conversational logic and memory (LangGraph, Gemini, tools, RAG, etc.).
- Synthesizes AI response to speech (e.g., ElevenLabs, Cartesia).
- Streams synthesized audio back to the user in real time.
- Handles session management, memory, and robust cleanup.

## Technology Stack
- **Frontend:** Flutter, Dart, livekit_client
- **Backend:** Python, LiveKit Agents, LangGraph, Gemini (Google Generative AI), ElevenLabs, Deepgram, Silero, asyncio
- **Other:** Docker (for deployment), YAML (for config), dotenv (for secrets)

## Process / Workflow
1. **User speaks into the Flutter app.**
2. **Audio is streamed to the LiveKit room** via WebRTC.
3. **LiveKit Agent receives audio, detects turns, and transcribes speech.**
4. **Transcribed text is sent to LangGraph (Gemini, tools, RAG, etc.)** for AI response, maintaining conversation history.
5. **AI response is synthesized to speech** (e.g., ElevenLabs) and streamed back to the user.
6. **Session and memory are managed** for each user connection by the agent.

## Key Implementation Details
- **LiveKit Room:** WebRTC-based, low-latency, bidirectional audio and control.
- **Session Memory:** Per-connection conversational memory using LangChain/LangGraph.
- **Plugin Architecture:** Swap LLM, TTS, STT, and VAD providers as needed.
- **Turn Detection & VAD:** Automatic, robust handling of speech segments and interruptions.
- **Error Handling:** Robust cleanup, reconnection, and error logging for all connection and processing steps.

## Migration Process
1. **Set up LiveKit server (Cloud or self-hosted).**
2. **Install LiveKit agent SDK and plugins (LangGraph, Gemini, ElevenLabs, etc.).**
3. **Port conversational logic to LiveKit agent using plugin system.**
4. **Connect Flutter frontend to LiveKit room.**
5. **Test end-to-end and iterate.**
6. **Deprecate legacy WebSocket/FastAPI backend.**

## Next Steps
- Complete LiveKit agent migration and test with Flutter app.
- Migrate all LangChain tools and memory to agent.
- Optimize for production (Docker, environment config, monitoring).
- (Optional) Add more AI providers or advanced workflows (multi-agent, vision, etc).

---

For further details, see the codebase or request specific implementation examples. 