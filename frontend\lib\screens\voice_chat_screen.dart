import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/livekit_service.dart' as livekit;

class VoiceChatScreen extends StatefulWidget {
  const VoiceChatScreen({super.key});

  @override
  State<VoiceChatScreen> createState() => _VoiceChatScreenState();
}

class _VoiceChatScreenState extends State<VoiceChatScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SolAI Assistant'),
        centerTitle: true,
        elevation: 0,
      ),
      body: Consumer<livekit.LiveKitService>(
        builder: (context, liveKitService, child) {
          return Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Status indicator
                _buildStatusIndicator(liveKitService),
                const SizedBox(height: 40),

                // Main voice button
                _buildVoiceButton(liveKitService),
                const SizedBox(height: 40),

                // Control buttons
                _buildControlButtons(liveKitService),
                const SizedBox(height: 40),

                // Connection info
                _buildConnectionInfo(liveKitService),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusIndicator(LiveKitService service) {
    String statusText;
    Color statusColor;
    IconData statusIcon;

    switch (service.connectionState) {
      case ConnectionState.disconnected:
        statusText = 'Disconnected';
        statusColor = Colors.grey;
        statusIcon = Icons.cloud_off;
        break;
      case ConnectionState.connecting:
        statusText = 'Connecting...';
        statusColor = Colors.orange;
        statusIcon = Icons.cloud_sync;
        break;
      case ConnectionState.connected:
        statusText = 'Connected';
        statusColor = Colors.green;
        statusIcon = Icons.cloud_done;
        break;
      case ConnectionState.error:
        statusText = 'Error';
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 16),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceButton(LiveKitService service) {
    final isListening = service.isListening && !service.isMuted;

    if (isListening) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
      _pulseController.reset();
    }

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isListening ? _pulseAnimation.value : 1.0,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isListening
                    ? [Colors.blue.shade400, Colors.blue.shade600]
                    : [Colors.grey.shade300, Colors.grey.shade500],
              ),
              boxShadow: [
                BoxShadow(
                  color: isListening
                      ? Colors.blue.withOpacity(0.3)
                      : Colors.grey.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              service.isMuted ? Icons.mic_off : Icons.mic,
              size: 48,
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButtons(LiveKitService service) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Connect/Disconnect button
        ElevatedButton.icon(
          onPressed: service.connectionState == ConnectionState.connecting
              ? null
              : () async {
                  if (service.isConnected) {
                    await service.disconnect();
                  } else {
                    await service.connect();
                  }
                },
          icon: Icon(
            service.isConnected ? Icons.call_end : Icons.call,
          ),
          label: Text(
            service.isConnected ? 'Disconnect' : 'Connect',
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: service.isConnected ? Colors.red : Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),

        // Mute/Unmute button
        ElevatedButton.icon(
          onPressed: service.isConnected
              ? () async {
                  await service.toggleMute();
                }
              : null,
          icon: Icon(
            service.isMuted ? Icons.mic_off : Icons.mic,
          ),
          label: Text(
            service.isMuted ? 'Unmute' : 'Mute',
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: service.isMuted ? Colors.orange : Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildConnectionInfo(LiveKitService service) {
    if (service.errorMessage != null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(height: 8),
            Text(
              'Error: ${service.errorMessage}',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (service.isConnected) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.withOpacity(0.3)),
        ),
        child: const Column(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(height: 8),
            Text(
              'Connected to SolAI Assistant\nSpeak naturally to start a conversation',
              style: TextStyle(color: Colors.green),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: const Column(
        children: [
          Icon(Icons.info, color: Colors.blue),
          SizedBox(height: 8),
          Text(
            'Welcome to SolAI Assistant\nTap "Connect" to start a voice conversation',
            style: TextStyle(color: Colors.blue),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
