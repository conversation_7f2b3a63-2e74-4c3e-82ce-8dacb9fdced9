"""LiveKit Agent implementation with LangGraph integration."""

import asyncio
import logging
from typing import Optional
from livekit.agents import JobContext, WorkerOptions, cli, llm
from livekit.plugins import openai, silero, deepgram, elevenlabs
from livekit import rtc
from langgraph_flow import ConversationFlow
from config import settings

logger = logging.getLogger(__name__)


class SolAIAgent:
    """Main agent class that integrates LiveKit with LangGraph."""

    def __init__(self):
        self.conversation_flow = ConversationFlow()
        self._setup_logging()

    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    async def entrypoint(self, ctx: JobContext):
        """Main entrypoint for the LiveKit agent."""
        logger.info(f"Starting {settings.agent_name} for room: {ctx.room.name}")

        # Setup TTS based on configuration
        if settings.tts_provider == "elevenlabs" and settings.elevenlabs_api_key:
            logger.info("Using ElevenLabs for TTS")
            tts = elevenlabs.TTS(
                api_key=settings.elevenlabs_api_key,
                voice=settings.elevenlabs_voice_id
            )
        else:
            logger.info("Using OpenAI for TTS")
            tts = openai.TTS(
                voice=settings.tts_voice,
                api_key=settings.openai_api_key
            )

        # Setup STT based on configuration
        if settings.stt_provider == "gemini" and settings.google_api_key:
            logger.info("Using Gemini for STT")
            # Note: Gemini doesn't have direct STT support in LiveKit yet
            # Falling back to OpenAI for now
            stt = openai.STT(api_key=settings.openai_api_key)
        elif settings.stt_provider == "deepgram" and settings.deepgram_api_key:
            logger.info("Using Deepgram for STT")
            stt = deepgram.STT(api_key=settings.deepgram_api_key)
        else:
            logger.info("Using OpenAI for STT")
            stt = openai.STT(api_key=settings.openai_api_key)

        # Wait for participant to join
        await ctx.wait_for_participant()
        logger.info("Participant joined, starting basic agent")

        # For now, let's create a simple agent that just logs
        # This is a basic implementation - we'll enhance it step by step
        logger.info("SolAI Agent is running and ready for connections")

        # Keep the agent alive
        while True:
            await asyncio.sleep(1)


def main():
    """Main function to run the agent."""
    agent = SolAIAgent()

    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=agent.entrypoint,
            prewarm_fnc=None,
        )
    )


if __name__ == "__main__":
    main()
