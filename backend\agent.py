"""LiveKit Agent implementation with LangGraph integration."""

import asyncio
import logging
from typing import Optional
from livekit.agents import AutoSubscribe, JobContext, WorkerOptions, cli, llm
from livekit.agents.voice_assistant import VoiceAssistant
from livekit.plugins import openai, silero, deepgram
from livekit import rtc
from langgraph_flow import ConversationFlow
from config import settings

logger = logging.getLogger(__name__)


class SolAIAgent:
    """Main agent class that integrates LiveKit with LangGraph."""
    
    def __init__(self):
        self.conversation_flow = ConversationFlow()
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def entrypoint(self, ctx: JobContext):
        """Main entrypoint for the LiveKit agent."""
        logger.info(f"Starting {settings.agent_name} for room: {ctx.room.name}")
        
        # Initialize AI components
        initial_ctx = llm.ChatContext().append(
            role="system",
            text=f"""
You are {settings.agent_name}, a helpful and friendly AI assistant.
You are having a real-time voice conversation with a user.
Keep your responses conversational, concise, and engaging.
Respond naturally as if you're speaking, not writing.
Avoid using markdown or special formatting in your responses.
"""
        )
        
        # Setup LLM
        if settings.google_api_key:
            # Use OpenAI for voice assistant (more stable for real-time)
            # but we'll use our LangGraph flow for actual conversation logic
            assistant_llm = openai.LLM(
                model="gpt-4o-mini",
                api_key=settings.openai_api_key
            )
        else:
            assistant_llm = openai.LLM(
                model=settings.llm_model,
                api_key=settings.openai_api_key
            )
        
        # Setup TTS
        tts = openai.TTS(
            voice=settings.tts_voice,
            api_key=settings.openai_api_key
        )
        
        # Setup STT
        if settings.deepgram_api_key:
            stt = deepgram.STT(api_key=settings.deepgram_api_key)
        else:
            stt = openai.STT(api_key=settings.openai_api_key)
        
        # Create custom LLM wrapper that uses our LangGraph flow
        class LangGraphLLM(llm.LLM):
            def __init__(self, conversation_flow: ConversationFlow):
                self.conversation_flow = conversation_flow
                self.session_id = ctx.room.name
            
            async def chat(
                self,
                *,
                chat_ctx: llm.ChatContext,
                conn_handle: Optional[object] = None,
                fnc_ctx: Optional[llm.FunctionContext] = None,
            ) -> "llm.LLMStream":
                # Extract the latest user message
                user_message = ""
                for msg in reversed(chat_ctx.messages):
                    if msg.role == "user":
                        user_message = msg.content
                        break
                
                if user_message:
                    # Process through LangGraph
                    response = await self.conversation_flow.process_message(
                        user_message=user_message,
                        user_id="user",  # In production, get from participant
                        session_id=self.session_id
                    )
                    
                    # Create a simple stream that yields the response
                    class SimpleStream:
                        def __init__(self, content: str):
                            self.content = content
                            self._yielded = False
                        
                        def __aiter__(self):
                            return self
                        
                        async def __anext__(self):
                            if not self._yielded:
                                self._yielded = True
                                return llm.ChatChunk(
                                    choices=[
                                        llm.Choice(
                                            delta=llm.ChoiceDelta(content=self.content),
                                            index=0
                                        )
                                    ]
                                )
                            else:
                                raise StopAsyncIteration
                    
                    return SimpleStream(response)
                else:
                    # Return empty stream if no user message
                    class EmptyStream:
                        def __aiter__(self):
                            return self
                        
                        async def __anext__(self):
                            raise StopAsyncIteration
                    
                    return EmptyStream()
        
        # Create voice assistant with our custom LLM
        langgraph_llm = LangGraphLLM(self.conversation_flow)
        
        assistant = VoiceAssistant(
            vad=silero.VAD.load(),
            stt=stt,
            llm=langgraph_llm,
            tts=tts,
            chat_ctx=initial_ctx,
        )
        
        # Start the assistant
        assistant.start(ctx.room)
        
        # Wait for participant to join
        await ctx.wait_for_participant()
        logger.info("Participant joined, assistant is ready")
        
        # Keep the agent running
        await assistant.say("Hello! I'm your AI assistant. How can I help you today?", allow_interruptions=True)


def main():
    """Main function to run the agent."""
    agent = SolAIAgent()
    
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=agent.entrypoint,
            prewarm_fnc=None,
        )
    )


if __name__ == "__main__":
    main()
