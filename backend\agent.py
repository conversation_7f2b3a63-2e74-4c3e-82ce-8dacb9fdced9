"""LiveKit Agent implementation with LangGraph integration."""

import asyncio
import logging
from typing import Optional
from livekit.agents import JobContext, WorkerOptions, cli, llm
from livekit.plugins import openai, silero, deepgram, elevenlabs
from livekit import rtc
from langgraph_flow import ConversationFlow
from config import settings

logger = logging.getLogger(__name__)


class SolAIAgent:
    """Main agent class that integrates LiveKit with LangGraph."""

    def __init__(self):
        self.conversation_flow = ConversationFlow()
        self._setup_logging()

    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    def _create_llm_adapter(self):
        """Create an LLM adapter that uses our LangGraph conversation flow."""

        class LangGraphLLMAdapter(llm.LLM):
            """Adapter to use LangGraph conversation flow with LiveKit."""

            def __init__(self, conversation_flow):
                self.conversation_flow = conversation_flow

            async def agenerate(self, *, chat_ctx: llm.ChatContext, **kwargs) -> llm.LLMStream:
                """Generate response using LangGraph flow."""
                try:
                    # Extract the latest user message
                    user_message = ""
                    if chat_ctx.messages:
                        user_message = chat_ctx.messages[-1].content

                    # Process through LangGraph
                    response = await self.conversation_flow.process_message(
                        user_message=user_message,
                        user_id="user",  # In production, get from context
                        session_id="session"  # In production, get from room/participant
                    )

                    # Create a simple stream that yields the response
                    async def response_stream():
                        yield llm.ChatChunk(
                            choices=[
                                llm.Choice(
                                    delta=llm.ChoiceDelta(content=response),
                                    index=0
                                )
                            ]
                        )

                    return llm.LLMStream(response_stream())

                except Exception as e:
                    logger.error(f"Error in LangGraph LLM adapter: {e}")
                    # Return error response
                    async def error_stream():
                        yield llm.ChatChunk(
                            choices=[
                                llm.Choice(
                                    delta=llm.ChoiceDelta(content="I'm sorry, I encountered an error. Could you please try again?"),
                                    index=0
                                )
                            ]
                        )
                    return llm.LLMStream(error_stream())

        return LangGraphLLMAdapter(self.conversation_flow)

    async def entrypoint(self, ctx: JobContext):
        """Main entrypoint for the LiveKit agent."""
        logger.info(f"Starting {settings.agent_name} for room: {ctx.room.name}")

        # Setup TTS based on configuration
        if settings.tts_provider == "elevenlabs" and settings.elevenlabs_api_key:
            logger.info("Using ElevenLabs for TTS")
            tts = elevenlabs.TTS(
                api_key=settings.elevenlabs_api_key,
                voice=settings.elevenlabs_voice_id
            )
        else:
            logger.info("Using OpenAI for TTS")
            tts = openai.TTS(
                voice=settings.tts_voice,
                api_key=settings.openai_api_key
            )

        # Setup STT based on configuration
        if settings.stt_provider == "gemini" and settings.google_api_key:
            logger.info("Using Gemini for STT")
            # Note: Gemini doesn't have direct STT support in LiveKit yet
            # Falling back to OpenAI for now
            stt = openai.STT(api_key=settings.openai_api_key)
        elif settings.stt_provider == "deepgram" and settings.deepgram_api_key:
            logger.info("Using Deepgram for STT")
            stt = deepgram.STT(api_key=settings.deepgram_api_key)
        else:
            logger.info("Using OpenAI for STT")
            stt = openai.STT(api_key=settings.openai_api_key)

        # Wait for participant to join
        await ctx.wait_for_participant()
        logger.info("Participant joined, starting LangGraph-powered conversation agent")

        # Create the conversation agent with LangGraph integration
        conversation_agent = llm.VoiceAssistant(
            vad=silero.VAD.load(),  # Voice Activity Detection
            stt=stt,  # Speech-to-Text
            llm=self._create_llm_adapter(),  # LLM with LangGraph
            tts=tts,  # Text-to-Speech
            chat_ctx=llm.ChatContext(
                messages=[
                    llm.ChatMessage(
                        role="system",
                        content=f"""You are {settings.agent_name}, a helpful and friendly AI assistant.
You are having a real-time voice conversation with a user.
Keep your responses conversational, concise, and engaging.
Respond naturally as if you're speaking, not writing.
Avoid long explanations unless specifically asked."""
                    )
                ]
            )
        )

        # Start the conversation agent
        conversation_agent.start(ctx.room)
        logger.info("LangGraph conversation agent started successfully")

        # Keep the agent alive and handle the conversation
        await conversation_agent.aclose()


async def prewarm(ctx):
    """Prewarm function for the agent."""
    logger.info("Prewarming SolAI agent...")
    # Initialize any resources that can be preloaded
    pass

def main():
    """Main function to run the agent."""
    agent = SolAIAgent()

    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=agent.entrypoint,
            prewarm_fnc=prewarm,
        )
    )


if __name__ == "__main__":
    main()
