import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

enum ConnectionState {
  disconnected,
  connecting,
  connected,
  error,
}

class LiveKitService extends ChangeNotifier {
  Room? _room;
  LocalAudioTrack? _audioTrack;
  ConnectionState _connectionState = ConnectionState.disconnected;
  String? _errorMessage;
  bool _isMuted = false;
  bool _isListening = false;

  // Getters
  Room? get room => _room;
  ConnectionState get connectionState => _connectionState;
  String? get errorMessage => _errorMessage;
  bool get isMuted => _isMuted;
  bool get isListening => _isListening;
  bool get isConnected => _connectionState == ConnectionState.connected;

  Future<void> connect() async {
    try {
      _setConnectionState(ConnectionState.connecting);

      // Request microphone permission
      final micPermission = await Permission.microphone.request();
      if (!micPermission.isGranted) {
        throw Exception('Microphone permission is required');
      }

      // Get configuration from environment
      final url = dotenv.env['LIVEKIT_URL'];
      final apiKey = dotenv.env['LIVEKIT_API_KEY'];
      final apiSecret = dotenv.env['LIVEKIT_API_SECRET'];

      if (url == null || apiKey == null || apiSecret == null) {
        throw Exception('LiveKit configuration not found in environment');
      }

      // Generate room name and token
      final roomName = 'solai-room-${DateTime.now().millisecondsSinceEpoch}';
      final token = await _generateToken(roomName, 'user', apiKey, apiSecret);

      // Create room and connect
      _room = Room();

      // Set up room listeners
      _room!.addListener(_onRoomUpdate);

      await _room!.connect(url, token);

      // Enable audio
      await _enableAudio();

      _setConnectionState(ConnectionState.connected);
    } catch (e) {
      _setConnectionState(ConnectionState.error);
      _errorMessage = e.toString();
      if (kDebugMode) {
        print('Connection error: $e');
      }
    }
  }

  Future<void> disconnect() async {
    try {
      if (_audioTrack != null) {
        await _audioTrack!.stop();
        _audioTrack = null;
      }

      if (_room != null) {
        _room!.removeListener(_onRoomUpdate);
        await _room!.disconnect();
        _room = null;
      }

      _setConnectionState(ConnectionState.disconnected);
      _isMuted = false;
      _isListening = false;
    } catch (e) {
      if (kDebugMode) {
        print('Disconnect error: $e');
      }
    }
  }

  Future<void> toggleMute() async {
    if (_audioTrack == null) return;

    try {
      _isMuted = !_isMuted;
      await _audioTrack!.setMuted(_isMuted);
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Toggle mute error: $e');
      }
    }
  }

  Future<void> _enableAudio() async {
    try {
      // Create audio track
      _audioTrack = await LocalAudioTrack.create(AudioCaptureOptions(
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      ));

      // Publish audio track
      await _room!.localParticipant!.publishAudioTrack(_audioTrack!);

      _isListening = true;
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Enable audio error: $e');
      }
      throw Exception('Failed to enable audio: $e');
    }
  }

  void _onRoomUpdate() {
    notifyListeners();
  }

  void _setConnectionState(ConnectionState state) {
    _connectionState = state;
    if (state != ConnectionState.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  // Generate token by calling our backend token server
  Future<String> _generateToken(String roomName, String identity, String apiKey, String apiSecret) async {
    try {
      // Call our token server (running on localhost:8080)
      final response = await http.post(
        Uri.parse('http://localhost:8080/token'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'room': roomName,
          'participant': identity,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['token'];
      } else {
        throw Exception('Failed to generate token: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Token generation error: $e');
      }
      throw Exception('Failed to generate token: $e');
    }
  }

  @override
  void dispose() {
    disconnect();
    super.dispose();
  }
}
