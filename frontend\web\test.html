<!DOCTYPE html>
<html>
<head>
    <title>LiveKit Connection Test</title>
    <script src="https://cdn.jsdelivr.net/npm/livekit-client@2.3.0/dist/livekit-client.umd.js"></script>
</head>
<body>
    <h1>LiveKit Connection Test</h1>
    <button onclick="testConnection()">Test Connection</button>
    <div id="status"></div>
    <div id="logs"></div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            logs.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        async function testConnection() {
            const status = document.getElementById('status');
            status.innerHTML = 'Testing...';

            try {
                log('Starting LiveKit connection test...');

                // Check if LiveKit is available
                log('Checking LiveKit availability...');
                log('Available globals: ' + Object.keys(window).filter(k => k.toLowerCase().includes('live')));

                // Try different possible names for the LiveKit client
                const LiveKit = window.LiveKitClient || window.LivekitClient || window.livekit || LiveKitClient || LivekitClient;

                if (!LiveKit) {
                    throw new Error('LiveKit client library not loaded');
                }

                log('LiveKit client loaded: ' + typeof LiveKit);
                log('LiveKit Room available: ' + typeof LiveKit.Room);

                // Get token from our server
                log('Requesting token...');
                const response = await fetch('http://localhost:8080/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        room: 'test-room-' + Date.now(),
                        participant: 'test-user'
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to get token: ' + response.status);
                }

                const tokenData = await response.json();
                log('Token received: ' + tokenData.token.substring(0, 20) + '...');
                log('Full token data: ' + JSON.stringify(tokenData));

                // Create room and connect
                log('Creating LiveKit room...');
                const room = new LiveKit.Room({
                    adaptiveStream: true,
                    dynacast: true,
                });

                room.on('connected', () => {
                    log('✅ Connected to LiveKit successfully!');
                    log('Room SID: ' + room.sid);
                    log('Local participant: ' + room.localParticipant.identity);
                    status.innerHTML = '✅ Connection successful!';
                });

                room.on('disconnected', (reason) => {
                    log('Disconnected from LiveKit: ' + reason);
                });

                room.on('connectionStateChanged', (state) => {
                    log('Connection state changed: ' + state);
                });

                room.on('error', (error) => {
                    log('❌ LiveKit error: ' + error.message);
                    log('Error details: ' + JSON.stringify(error));
                    status.innerHTML = '❌ Connection failed: ' + error.message;
                });

                log('Connecting to: ' + tokenData.url);
                log('Token length: ' + tokenData.token.length);

                // Add timeout to connection attempt
                const connectPromise = room.connect(tokenData.url, tokenData.token, {
                    autoSubscribe: true,
                });

                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Connection timeout after 10 seconds')), 10000);
                });

                await Promise.race([connectPromise, timeoutPromise]);

            } catch (error) {
                log('❌ Test failed: ' + error.message);
                log('Error stack: ' + error.stack);
                status.innerHTML = '❌ Test failed: ' + error.message;
            }
        }
    </script>
</body>
</html>
